<?php

namespace App\Http\Controllers;

use App\Models\AutomationWorkflow;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Models\AnalyticsEvent;
use App\Services\WorkflowEngine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WorkflowController extends Controller
{
    protected $workflowEngine;

    public function __construct(WorkflowEngine $workflowEngine)
    {
        $this->workflowEngine = $workflowEngine;
    }

    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $query = AutomationWorkflow::with(['creator'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->has('trigger_type')) {
            $query->where('trigger_type', $request->trigger_type);
        }

        $workflows = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => [
                'workflows' => $workflows->items(),
                'pagination' => [
                    'current_page' => $workflows->currentPage(),
                    'last_page' => $workflows->lastPage(),
                    'per_page' => $workflows->perPage(),
                    'total' => $workflows->total()
                ]
            ]
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'trigger_type' => 'required|in:project_created,project_status_changed,task_created,task_completed,booking_confirmed,deadline_approaching',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'required|array',
            'actions.*.type' => 'required|in:create_task,send_notification,assign_user,update_status,send_email',
            'actions.*.config' => 'required|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['created_by'] = $request->user()->id;

        $workflow = AutomationWorkflow::create($data);

        // track workflow creation
        AnalyticsEvent::track('workflow_created', 'workflow', $workflow);

        return response()->json([
            'success' => true,
            'message' => 'Workflow created successfully',
            'data' => ['workflow' => $workflow->load('creator')]
        ], 201);
    }

    public function show(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->load(['creator', 'executions' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'data' => ['workflow' => $workflow]
        ]);
    }

    public function update(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'sometimes|array',
            'actions.*.type' => 'required_with:actions|in:create_task,send_notification,assign_user,update_status,send_email',
            'actions.*.config' => 'required_with:actions|array',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $workflow->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Workflow updated successfully',
            'data' => ['workflow' => $workflow->fresh(['creator'])]
        ]);
    }

    public function destroy(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Workflow deleted successfully'
        ]);
    }

    public function toggle(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->update(['is_active' => !$workflow->is_active]);

        return response()->json([
            'success' => true,
            'message' => $workflow->is_active ? 'Workflow activated' : 'Workflow deactivated',
            'data' => ['workflow' => $workflow->fresh()]
        ]);
    }

    public function execute(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'trigger_data' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->workflowEngine->execute($workflow, $request->trigger_data);

            return response()->json([
                'success' => true,
                'message' => 'Workflow executed successfully',
                'data' => ['result' => $result]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Workflow execution failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function templates(Request $request)
    {
        $templates = [
            [
                'id' => 'new_project_setup',
                'name' => 'New Project Setup',
                'description' => 'Automatically create initial tasks when a new project is created',
                'trigger_type' => 'project_created',
                'actions' => [
                    [
                        'type' => 'create_task',
                        'config' => [
                            'title' => 'Initial client consultation',
                            'department_id' => 1,
                            'priority' => 'high',
                            'due_days' => 1
                        ]
                    ],
                    [
                        'type' => 'create_task',
                        'config' => [
                            'title' => 'Setup project files and folders',
                            'department_id' => 2,
                            'priority' => 'medium',
                            'due_days' => 2
                        ]
                    ],
                    [
                        'type' => 'send_notification',
                        'config' => [
                            'message' => 'New project created: {{project.title}}',
                            'channels' => ['general']
                        ]
                    ]
                ]
            ],
            [
                'id' => 'task_completion_flow',
                'name' => 'Task Completion Flow',
                'description' => 'Notify team and create follow-up tasks when a task is completed',
                'trigger_type' => 'task_completed',
                'actions' => [
                    [
                        'type' => 'send_notification',
                        'config' => [
                            'message' => 'Task completed: {{task.title}} by {{user.name}}',
                            'channels' => ['project-{{project.id}}']
                        ]
                    ],
                    [
                        'type' => 'update_status',
                        'config' => [
                            'entity' => 'project',
                            'status' => 'in_progress'
                        ]
                    ]
                ]
            ],
            [
                'id' => 'deadline_reminder',
                'name' => 'Deadline Reminder',
                'description' => 'Send reminders when deadlines are approaching',
                'trigger_type' => 'deadline_approaching',
                'trigger_conditions' => [
                    'days_before' => 2
                ],
                'actions' => [
                    [
                        'type' => 'send_email',
                        'config' => [
                            'to' => '{{assigned_user.email}}',
                            'subject' => 'Deadline Reminder: {{task.title}}',
                            'template' => 'deadline_reminder'
                        ]
                    ],
                    [
                        'type' => 'send_notification',
                        'config' => [
                            'message' => 'Deadline approaching: {{task.title}} due in {{days}} days',
                            'user_id' => '{{assigned_user.id}}'
                        ]
                    ]
                ]
            ],
            [
                'id' => 'booking_confirmation',
                'name' => 'Booking Confirmation Flow',
                'description' => 'Automatically handle booking confirmations and setup',
                'trigger_type' => 'booking_confirmed',
                'actions' => [
                    [
                        'type' => 'send_email',
                        'config' => [
                            'to' => '{{client.email}}',
                            'subject' => 'Booking Confirmed: {{studio_room.name}}',
                            'template' => 'booking_confirmation'
                        ]
                    ],
                    [
                        'type' => 'create_task',
                        'config' => [
                            'title' => 'Prepare studio for {{client.name}}',
                            'department_id' => 3,
                            'priority' => 'high',
                            'due_date' => '{{booking.start_time}}'
                        ]
                    ]
                ]
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => ['templates' => $templates]
        ]);
    }

    public function createFromTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|string',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $templates = collect($this->templates($request)->getData()->data->templates);
        $template = $templates->firstWhere('id', $request->template_id);

        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found'
            ], 404);
        }

        $workflow = AutomationWorkflow::create([
            'name' => $request->name,
            'description' => $request->description ?: $template->description,
            'trigger_type' => $template->trigger_type,
            'trigger_conditions' => $template->trigger_conditions ?? [],
            'actions' => $template->actions,
            'is_active' => true,
            'created_by' => $request->user()->id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Workflow created from template successfully',
            'data' => ['workflow' => $workflow->load('creator')]
        ], 201);
    }
}
