@extends('layouts.dashboard')

@section('title', 'Bookings')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Studio Bookings</h1>
    <div class="flex space-x-3">
        <button onclick="openCreateBookingModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-plus"></i>
            <span>New Booking</span>
        </button>
        <button onclick="checkAvailability()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-calendar-check"></i>
            <span>Check Availability</span>
        </button>
    </div>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="statusFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Studio Room</label>
                <select id="roomFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Rooms</option>
                    @foreach($rooms as $room)
                        <option value="{{ $room->id }}">{{ $room->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                <select id="clientFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Clients</option>
                    @foreach($clients as $client)
                        <option value="{{ $client->id }}">{{ $client->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date From</label>
                <input type="date" id="dateFromFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date To</label>
                <input type="date" id="dateToFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                <div class="flex space-x-2">
                    <button onclick="applyBookingFilters()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Filter</button>
                    <button onclick="clearBookingFilters()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Clear</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-blue-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar text-blue-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Total Bookings</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="totalBookings">{{ $summary['total'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-green-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-green-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Today's Sessions</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="todayBookings">{{ $summary['today'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-yellow-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar-week text-yellow-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">This Week</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="weekBookings">{{ $summary['this_week'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-purple-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-dollar-sign text-purple-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Revenue</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="totalRevenue">₦{{ number_format($summary['revenue'] ?? 0, 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bookings Grid -->
<div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Bookings will be loaded here via AJAX -->
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="flex justify-center items-center py-12 hidden">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
</div>

<!-- Pagination -->
<div id="paginationContainer" class="flex justify-center mt-8">
    <!-- Pagination will be loaded here -->
</div>
<!-- Create/Edit Booking Modal -->
<div id="bookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900" id="bookingModalTitle">Create Booking</h3>
            <button onclick="closeBookingModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="bookingForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Client *</label>
                    <div class="relative">
                        <input type="text" id="clientSearch" placeholder="Search clients..."
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                               autocomplete="off">
                        <input type="hidden" name="client_id" id="bookingClient" required>
                        <div id="clientDropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                            <!-- Client options will be populated here -->
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Studio Room *</label>
                    <select name="studio_room_id" id="bookingRoom" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="">Select Room</option>
                        @foreach($rooms as $room)
                            <option value="{{ $room->id }}">{{ $room->name }} - ₦{{ number_format($room->hourly_rate, 0) }}/hr</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Date *</label>
                    <input type="date" name="start_date" id="bookingStartDate" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Time *</label>
                    <input type="time" name="start_time" id="bookingStartTime" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Duration (hours) *</label>
                    <input type="number" name="duration" id="bookingDuration" required min="1" max="24" step="0.5" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="bookingStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Total Cost (₦)</label>
                    <input type="number" name="total_cost" id="bookingTotalCost" step="0.01" readonly class="w-full border border-gray-300 rounded-lg px-3 py-2 bg-gray-100 focus:outline-none">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Session Type</label>
                    <select name="session_type" id="bookingSessionType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="recording">Recording</option>
                        <option value="mixing">Mixing</option>
                        <option value="mastering">Mastering</option>
                        <option value="rehearsal">Rehearsal</option>
                        <option value="live_session">Live Session</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea name="notes" id="bookingNotes" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
            </div>

            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeBookingModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <span id="bookingSubmitButtonText">Create Booking</span>
                </button>
            </div>
        </form>
    </div>
</div>
<!-- View Booking Modal -->
<div id="viewBookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Booking Details</h3>
            <button onclick="closeViewBookingModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div id="bookingDetails">
            <!-- Booking details will be loaded here -->
        </div>
    </div>
</div>

<!-- Availability Check Modal -->
<div id="availabilityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Check Availability</h3>
            <button onclick="closeAvailabilityModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="availabilityForm" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Studio Room</label>
                    <select id="availabilityRoom" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="">All Rooms</option>
                        @foreach($rooms as $room)
                            <option value="{{ $room->id }}">{{ $room->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                    <input type="date" id="availabilityDate" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                    <input type="time" id="availabilityStartTime" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Duration (hours)</label>
                    <input type="number" id="availabilityDuration" min="1" max="24" step="0.5" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
            </div>

            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeAvailabilityModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Check Availability</button>
            </div>
        </form>

        <div id="availabilityResults" class="mt-6 hidden">
            <!-- Availability results will be shown here -->
        </div>
    </div>
</div>

<!-- Action Modals -->
<div id="confirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <i class="fas fa-check text-green-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Confirm Booking</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to confirm this booking?</p>
            </div>
            <div class="flex justify-center space-x-4 mt-4">
                <button type="button" onclick="closeConfirmModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded">Cancel</button>
                <button type="button" onclick="confirmBookingAction()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-times text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Cancel Booking</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to cancel this booking?</p>
                <textarea id="cancelReason" placeholder="Cancellation reason..." class="w-full mt-2 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" rows="3"></textarea>
            </div>
            <div class="flex justify-center space-x-4 mt-4">
                <button type="button" onclick="closeCancelModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded">Cancel</button>
                <button type="button" onclick="cancelBookingAction()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">Cancel Booking</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentBookingPage = 1;
let currentBookingId = null;

// Load bookings on page load
$(document).ready(function() {
    loadBookings();

    // Auto-apply filters on change
    $('#statusFilter, #roomFilter, #clientFilter, #dateFromFilter, #dateToFilter').change(function() {
        currentBookingPage = 1;
        loadBookings();
    });

    // Calculate total cost when room or duration changes
    $('#bookingRoom, #bookingDuration').change(function() {
        calculateTotalCost();
    });

    // Handle booking form submission
    $('#bookingForm').on('submit', function(e) {
        e.preventDefault();
        submitBookingForm();
    });

    // Handle availability form submission
    $('#availabilityForm').on('submit', function(e) {
        e.preventDefault();
        checkRoomAvailability();
    });
});

// Load bookings via AJAX
function loadBookings() {
    $('#loadingSpinner').removeClass('hidden');
    $('#bookingsContainer').addClass('opacity-50');

    const filters = {
        status: $('#statusFilter').val(),
        room: $('#roomFilter').val(),
        client: $('#clientFilter').val(),
        date_from: $('#dateFromFilter').val(),
        date_to: $('#dateToFilter').val(),
        page: currentBookingPage
    };

    $.get('{{ route("bookings.index") }}', filters)
        .done(function(response) {
            if (response.success) {
                renderBookings(response.data.bookings);
                renderBookingPagination(response.data.pagination);
                updateBookingSummaryCards(response.data.summary);
            }
        })
        .fail(function() {
            showBookingNotification('Error loading bookings', 'error');
        })
        .always(function() {
            $('#loadingSpinner').addClass('hidden');
            $('#bookingsContainer').removeClass('opacity-50');
        });
}

// Render bookings grid
function renderBookings(bookings) {
    const container = $('#bookingsContainer');

    if (bookings.length === 0) {
        container.html(`
            <div class="col-span-full text-center py-12">
                <i class="fas fa-calendar-times text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
                <p class="text-gray-500 mb-4">Create your first booking to get started.</p>
                <button onclick="openCreateBookingModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Create Booking</button>
            </div>
        `);
        return;
    }

    let html = '';
    bookings.forEach(booking => {
        const statusColor = getBookingStatusColor(booking.status);
        const isToday = new Date(booking.start_time).toDateString() === new Date().toDateString();

        html += `
            <div class="bg-white overflow-hidden shadow rounded-lg ${isToday ? 'border-l-4 border-yellow-400' : ''}">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">#${booking.booking_number || booking.id}</h3>
                            <p class="text-sm text-gray-500">${booking.client ? booking.client.name : 'Walk-in'}</p>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                            ${booking.status.replace('_', ' ').toUpperCase()}
                        </span>
                    </div>

                    <div class="space-y-3 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-door-open text-gray-400 mr-2"></i>
                            <span>${booking.studio_room ? booking.studio_room.name : 'N/A'}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar text-gray-400 mr-2"></i>
                            <span>${formatDateTime(booking.start_time)} - ${formatTime(booking.end_time)}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock text-gray-400 mr-2"></i>
                            <span>${calculateDuration(booking.start_time, booking.end_time)}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-dollar-sign text-gray-400 mr-2"></i>
                            <span class="font-semibold">₦${formatNumber(booking.total_cost || 0)}</span>
                        </div>
                    </div>

                    ${booking.session_type ? `
                        <div class="mb-4">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                ${booking.session_type.replace('_', ' ').toUpperCase()}
                            </span>
                        </div>
                    ` : ''}

                    <div class="flex flex-wrap gap-2">
                        <button onclick="viewBooking(${booking.id})" class="bg-indigo-600 hover:bg-indigo-700 text-white py-1 px-3 rounded text-sm">View</button>
                        <button onclick="editBooking(${booking.id})" class="bg-gray-300 hover:bg-gray-400 text-gray-700 py-1 px-3 rounded text-sm">Edit</button>
                        ${booking.status === 'pending' ? `<button onclick="confirmBooking(${booking.id})" class="bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm">Confirm</button>` : ''}
                        ${booking.status === 'confirmed' && isToday ? `<button onclick="checkInBooking(${booking.id})" class="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm">Check In</button>` : ''}
                        ${booking.status === 'in_progress' ? `<button onclick="checkOutBooking(${booking.id})" class="bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded text-sm">Check Out</button>` : ''}
                        <button onclick="cancelBooking(${booking.id})" class="bg-red-600 hover:bg-red-700 text-white py-1 px-3 rounded text-sm">Cancel</button>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

// Helper functions and modal functions would continue here...
// Due to length constraints, I'll add them in the next chunk

function getBookingStatusColor(status) {
    const colors = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'confirmed': 'bg-green-100 text-green-800',
        'in_progress': 'bg-blue-100 text-blue-800',
        'completed': 'bg-purple-100 text-purple-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
}

function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

function calculateDuration(start, end) {
    const startTime = new Date(start);
    const endTime = new Date(end);
    const diffMs = endTime - startTime;
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
}

function formatTimeSlot(timeString) {
    // Handle different time formats
    let date;

    if (timeString.includes('T')) {
        // ISO format: 2025-09-01T09:00:00
        date = new Date(timeString);
    } else if (timeString.includes(' ')) {
        // Format: 2025-09-01 09:00:00
        date = new Date(timeString);
    } else if (timeString.includes(':')) {
        // Time only: 09:00:00 or 09:00
        const today = new Date().toISOString().split('T')[0];
        date = new Date(`${today}T${timeString}`);
    } else {
        // Fallback
        date = new Date(timeString);
    }

    // Format to readable time
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Client search functionality
const clients = @json($clients);
let filteredClients = clients;

function initializeClientSearch() {
    const searchInput = $('#clientSearch');
    const dropdown = $('#clientDropdown');
    const hiddenInput = $('#bookingClient');

    // Show all clients initially
    populateClientDropdown(clients);

    // Search functionality
    searchInput.on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filteredClients = clients.filter(client =>
            client.name.toLowerCase().includes(searchTerm) ||
            client.email.toLowerCase().includes(searchTerm) ||
            (client.company && client.company.toLowerCase().includes(searchTerm))
        );
        populateClientDropdown(filteredClients);
        dropdown.removeClass('hidden');
    });

    // Show dropdown on focus
    searchInput.on('focus', function() {
        populateClientDropdown(filteredClients);
        dropdown.removeClass('hidden');
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.relative').length) {
            dropdown.addClass('hidden');
        }
    });
}

function populateClientDropdown(clientList) {
    const dropdown = $('#clientDropdown');

    if (clientList.length === 0) {
        dropdown.html('<div class="px-3 py-2 text-gray-500">No clients found</div>');
        return;
    }

    let html = '';
    clientList.forEach(client => {
        html += `
            <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer client-option" data-client-id="${client.id}" data-client-name="${client.name}">
                <div class="font-medium">${client.name}</div>
                <div class="text-sm text-gray-500">${client.email}</div>
                ${client.company ? `<div class="text-xs text-gray-400">${client.company}</div>` : ''}
            </div>
        `;
    });

    dropdown.html(html);

    // Handle client selection
    $('.client-option').on('click', function() {
        const clientId = $(this).data('client-id');
        const clientName = $(this).data('client-name');

        $('#clientSearch').val(clientName);
        $('#bookingClient').val(clientId);
        $('#clientDropdown').addClass('hidden');
    });
}

// Modal functions
function openCreateBookingModal() {
    $('#bookingModalTitle').text('Create Booking');
    $('#bookingSubmitButtonText').text('Create Booking');
    $('#bookingForm')[0].reset();
    $('#clientSearch').val('');
    $('#bookingClient').val('');
    currentBookingId = null;
    $('#bookingModal').removeClass('hidden');

    // Initialize client search
    setTimeout(() => {
        initializeClientSearch();
    }, 100);
}

function closeBookingModal() {
    $('#bookingModal').addClass('hidden');
    currentBookingId = null;
}

function checkAvailability() {
    $('#availabilityModal').removeClass('hidden');
}

function closeAvailabilityModal() {
    $('#availabilityModal').addClass('hidden');
    $('#availabilityResults').addClass('hidden');
}

function checkRoomAvailability() {
    const roomId = $('#availabilityRoom').val();
    const date = $('#availabilityDate').val();
    const duration = $('#availabilityDuration').val();

    if (!roomId || !date || !duration) {
        showBookingNotification('Please fill in all fields', 'error');
        return;
    }

    // Show loading state
    const submitBtn = $('#availabilityForm button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Checking...');

    $.ajax({
        url: '{{ route("bookings.availability") }}',
        method: 'GET',
        data: {
            studio_room_id: roomId,
            start_date: date,
            duration: duration
        },
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .done(function(response) {
        if (response.success) {
            displayAvailabilityResults(response.data);
        } else {
            showBookingNotification('Error checking availability: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Availability check error:', xhr);
        let errorMessage = 'Error checking availability';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        }
        showBookingNotification(errorMessage, 'error');
    })
    .always(function() {
        // Reset loading state
        submitBtn.prop('disabled', false).text(originalText);
    });
}

function displayAvailabilityResults(data) {
    const resultsDiv = $('#availabilityResults');
    const availableSlots = data.available_slots || [];

    if (availableSlots.length === 0) {
        resultsDiv.html(`
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-red-400 text-4xl mb-4"></i>
                <h4 class="text-lg font-medium text-gray-900 mb-2">No Available Slots</h4>
                <p class="text-gray-500">No available time slots found for the selected date and duration.</p>
            </div>
        `);
    } else {
        let slotsHtml = `
            <div class="mb-4">
                <h4 class="text-lg font-medium text-gray-900 mb-2">Available Time Slots</h4>
                <p class="text-sm text-gray-600 mb-4">Found ${availableSlots.length} available slot(s)</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
        `;

        availableSlots.forEach(slot => {
            const startTime = formatTimeSlot(slot.start_time);
            const endTime = formatTimeSlot(slot.end_time);
            slotsHtml += `
                <div class="bg-green-50 border border-green-200 rounded-lg p-3 text-center hover:bg-green-100 transition-colors cursor-pointer">
                    <div class="text-sm font-medium text-green-800">${startTime}</div>
                    <div class="text-xs text-green-600">to ${endTime}</div>
                </div>
            `;
        });

        slotsHtml += '</div>';
        resultsDiv.html(slotsHtml);
    }

    resultsDiv.removeClass('hidden');
}

function calculateTotalCost() {
    const roomSelect = $('#bookingRoom');
    const duration = parseFloat($('#bookingDuration').val()) || 0;

    if (roomSelect.val() && duration > 0) {
        const roomText = roomSelect.find('option:selected').text();
        const rateMatch = roomText.match(/₦([\d,]+)/);
        if (rateMatch) {
            const hourlyRate = parseFloat(rateMatch[1].replace(/,/g, ''));
            const totalCost = hourlyRate * duration;
            $('#bookingTotalCost').val(totalCost.toFixed(2));
        }
    }
}

function applyBookingFilters() {
    currentBookingPage = 1;
    loadBookings();
}

function clearBookingFilters() {
    $('#statusFilter').val('');
    $('#roomFilter').val('');
    $('#clientFilter').val('');
    $('#dateFromFilter').val('');
    $('#dateToFilter').val('');
    currentBookingPage = 1;
    loadBookings();
}

function submitBookingForm() {
    const formData = new FormData(document.getElementById('bookingForm'));
    const submitButton = $('#bookingForm button[type="submit"]');
    const submitButtonText = $('#bookingSubmitButtonText');

    // Show loading state
    submitButton.prop('disabled', true);
    submitButtonText.html('<i class="fas fa-spinner fa-spin mr-2"></i>Creating...');

    // Convert FormData to regular object
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    // Add CSRF token
    data._token = $('meta[name="csrf-token"]').attr('content');

    console.log('Submitting booking data:', data);

    $.ajax({
        url: '{{ route("bookings.store") }}',
        method: 'POST',
        data: data,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .done(function(response) {
        console.log('Booking response:', response);
        if (response.success) {
            showBookingNotification('Booking created successfully!', 'success');
            closeBookingModal();
            // Reset form
            $('#bookingForm')[0].reset();
            // Reload the bookings list
            loadBookings();
        } else {
            showBookingNotification('Error: ' + (response.message || 'Unknown error'), 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Booking submission error:', xhr);
        let errorMessage = 'Error creating booking';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = Object.values(xhr.responseJSON.errors).flat();
            errorMessage += ': ' + errors.join(', ');
        }
        showBookingNotification(errorMessage, 'error');
    })
    .always(function() {
        // Reset loading state
        submitButton.prop('disabled', false);
        submitButtonText.text('Create Booking');
    });
}

function showBookingNotification(message, type) {
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const notification = $(`
        <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50">
            ${message}
        </div>
    `);

    $('body').append(notification);
    setTimeout(() => notification.remove(), 3000);
}

// Placeholder functions for other operations
function viewBooking(id) { /* Implementation */ }
function editBooking(id) { /* Implementation */ }
function confirmBooking(id) { currentBookingId = id; $('#confirmModal').removeClass('hidden'); }
function cancelBooking(id) { currentBookingId = id; $('#cancelModal').removeClass('hidden'); }
function checkInBooking(id) { /* Implementation */ }
function checkOutBooking(id) { /* Implementation */ }

function closeConfirmModal() { $('#confirmModal').addClass('hidden'); }
function closeCancelModal() { $('#cancelModal').addClass('hidden'); }

function confirmBookingAction() {
    // AJAX call to confirm booking
    closeConfirmModal();
    loadBookings();
}

function cancelBookingAction() {
    // AJAX call to cancel booking
    closeCancelModal();
    loadBookings();
}

function renderBookingPagination(pagination) { /* Implementation */ }
function updateBookingSummaryCards(summary) { /* Implementation */ }
</script>
@endpush
