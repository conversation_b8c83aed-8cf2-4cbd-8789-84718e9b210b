<?php $__env->startSection('title', 'Financial Dashboard'); ?>

<?php $__env->startSection('dashboard-content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Financial Dashboard</h1>
                        <p class="text-gray-600">Monitor your studio's financial performance and analytics</p>
                    </div>
                    <div class="flex space-x-3">
                        <select id="period-filter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="365">Last year</option>
                        </select>
                        <button onclick="openExportModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-download mr-2"></i>Export Report
                        </button>
                    </div>
                </div>

                <!-- Key Metrics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Revenue Card -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="total-revenue">
                                        ₦<?php echo e(number_format($data['revenue']['total'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm <?php echo e(($data['revenue']['growth_rate'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                        <?php echo e(($data['revenue']['growth_rate'] ?? 0) >= 0 ? '+' : ''); ?><?php echo e(number_format($data['revenue']['growth_rate'] ?? 0, 1)); ?>% from last period
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expenses Card -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Expenses</p>
                                    <p class="text-2xl font-semibold text-gray-900" id="total-expenses">
                                        ₦<?php echo e(number_format($data['expenses']['total'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        ₦<?php echo e(number_format($data['expenses']['cost_per_day'] ?? 0, 2)); ?>/day average
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit Card -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 <?php echo e(($data['profit']['gross_profit'] ?? 0) >= 0 ? 'bg-blue-100' : 'bg-red-100'); ?> rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 <?php echo e(($data['profit']['gross_profit'] ?? 0) >= 0 ? 'text-blue-600' : 'text-red-600'); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Gross Profit</p>
                                    <p class="text-2xl font-semibold <?php echo e(($data['profit']['gross_profit'] ?? 0) >= 0 ? 'text-gray-900' : 'text-red-600'); ?>" id="gross-profit">
                                        ₦<?php echo e(number_format($data['profit']['gross_profit'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo e(number_format($data['profit']['profit_margin'] ?? 0, 1)); ?>% margin
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Flow Card -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 <?php echo e(($data['cash_flow']['net_cash_flow'] ?? 0) >= 0 ? 'bg-purple-100' : 'bg-red-100'); ?> rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 <?php echo e(($data['cash_flow']['net_cash_flow'] ?? 0) >= 0 ? 'text-purple-600' : 'text-red-600'); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Net Cash Flow</p>
                                    <p class="text-2xl font-semibold <?php echo e(($data['cash_flow']['net_cash_flow'] ?? 0) >= 0 ? 'text-gray-900' : 'text-red-600'); ?>" id="net-cash-flow">
                                        ₦<?php echo e(number_format($data['cash_flow']['net_cash_flow'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo e($data['cash_flow']['cash_conversion_cycle'] ?? 0); ?> days cycle
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Revenue Trend Chart -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Trend</h3>
                            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                <canvas id="revenue-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Breakdown Chart -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Expense Breakdown</h3>
                            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                <canvas id="expense-chart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Breakdown -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Revenue Breakdown -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Breakdown</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Daily Average</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['revenue']['average_daily'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Monthly Projection</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['revenue']['projected_monthly'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Growth Rate</span>
                                    <span class="font-medium <?php echo e(($data['revenue']['growth_rate'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                        <?php echo e(($data['revenue']['growth_rate'] ?? 0) >= 0 ? '+' : ''); ?><?php echo e(number_format($data['revenue']['growth_rate'] ?? 0, 1)); ?>%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expense Breakdown -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Expense Breakdown</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Staff Costs</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['expenses']['staff_costs'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Equipment</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['expenses']['equipment_costs'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Operations</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['expenses']['operational_costs'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="border-t pt-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-900">Total</span>
                                        <span class="font-semibold">₦<?php echo e(number_format($data['expenses']['total'] ?? 0, 2)); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profit Analysis -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Profit Analysis</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">
                                    ₦<?php echo e(number_format($data['profit']['gross_profit'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Gross Profit</div>
                                <div class="text-sm font-medium <?php echo e(($data['profit']['profit_margin'] ?? 0) >= 20 ? 'text-green-600' : (($data['profit']['profit_margin'] ?? 0) >= 10 ? 'text-yellow-600' : 'text-red-600')); ?>">
                                    <?php echo e(number_format($data['profit']['profit_margin'] ?? 0, 1)); ?>% Margin
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">
                                    ₦<?php echo e(number_format($data['profit']['break_even_point'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Break-even Point</div>
                                <div class="text-sm text-gray-500">Monthly target</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">
                                    ₦<?php echo e(number_format($data['profit']['target_profit'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Target Profit</div>
                                <div class="text-sm text-gray-500">30% of expenses</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cash Flow Analysis -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Cash Flow Analysis</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold <?php echo e(($data['cash_flow']['net_cash_flow'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                    ₦<?php echo e(number_format($data['cash_flow']['net_cash_flow'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Net Cash Flow</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">
                                    ₦<?php echo e(number_format($data['cash_flow']['operating_cash_flow'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Operating Cash Flow</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">
                                    <?php echo e($data['cash_flow']['cash_conversion_cycle'] ?? 0); ?>

                                </div>
                                <div class="text-sm text-gray-600">Days to Convert</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-600">
                                    ₦<?php echo e(number_format($data['cash_flow']['burn_rate'] ?? 0, 2)); ?>

                                </div>
                                <div class="text-sm text-gray-600">Daily Burn Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize charts
        initializeRevenueChart();
        initializeExpenseChart();
        
        // Period filter change handler
        document.getElementById('period-filter').addEventListener('change', function() {
            const period = this.value;
            window.location.href = `<?php echo e(route('financial.dashboard')); ?>?period=${period}`;
        });
    });
    
    function initializeRevenueChart() {
        const ctx = document.getElementById('revenue-chart').getContext('2d');
        const trends = <?php echo json_encode($data['trends'] ?? [], 15, 512) ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trends.map(t => new Date(t.date).toLocaleDateString()),
                datasets: [{
                    label: 'Revenue',
                    data: trends.map(t => t.revenue),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₦' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }
    
    function initializeExpenseChart() {
        const ctx = document.getElementById('expense-chart').getContext('2d');
        const expenses = <?php echo json_encode($data['expenses'] ?? [], 15, 512) ?>;
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Staff Costs', 'Equipment', 'Operations'],
                datasets: [{
                    data: [
                        expenses.staff_costs || 0,
                        expenses.equipment_costs || 0,
                        expenses.operational_costs || 0
                    ],
                    backgroundColor: [
                        'rgb(239, 68, 68)',
                        'rgb(245, 158, 11)',
                        'rgb(34, 197, 94)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₦' + context.parsed.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }
    
    function openExportModal() {
        $('#exportModal').removeClass('hidden');
    }

    function closeExportModal() {
        $('#exportModal').addClass('hidden');
    }

    function exportReport() {
        const period = document.getElementById('period-filter').value;
        const format = document.getElementById('exportFormat').value;
        const includeCharts = document.getElementById('includeCharts').checked;
        const includeDetails = document.getElementById('includeDetails').checked;

        const params = new URLSearchParams({
            period: period,
            format: format,
            include_charts: includeCharts ? '1' : '0',
            include_details: includeDetails ? '1' : '0'
        });

        window.open(`<?php echo e(route('financial.dashboard')); ?>/export?${params.toString()}`, '_blank');
        closeExportModal();
    }
</script>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export Financial Report</h3>
                <button onclick="closeExportModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label for="exportFormat" class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                    <select id="exportFormat" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        <option value="pdf">PDF Report</option>
                        <option value="excel">Excel Spreadsheet</option>
                        <option value="csv">CSV Data</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Include Options</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="includeCharts" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Include Charts & Graphs</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="includeDetails" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Include Detailed Transactions</span>
                        </label>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                The report will include data for the selected time period and current filters.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeExportModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                    Cancel
                </button>
                <button onclick="exportReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/financial/dashboard.blade.php ENDPATH**/ ?>