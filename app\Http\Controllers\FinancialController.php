<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Project;
use App\Models\Client;
use App\Models\User;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinancialController extends Controller
{
    public function dashboard(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_financials')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);
        $endDate = now();

        $dashboard = [
            'revenue' => $this->getRevenueMetrics($startDate, $endDate),
            'expenses' => $this->getExpenseMetrics($startDate, $endDate),
            'profit' => $this->getProfitMetrics($startDate, $endDate),
            'cash_flow' => $this->getCashFlowMetrics($startDate, $endDate),
            'client_analytics' => $this->getClientFinancialAnalytics($startDate, $endDate),
            'project_profitability' => $this->getProjectProfitability($startDate, $endDate),
            'studio_utilization' => $this->getStudioUtilizationRevenue($startDate, $endDate),
            'forecasting' => $this->getRevenueForecast(),
            'trends' => $this->getFinancialTrends($startDate, $endDate)
        ];

        return view('financial.dashboard', [
            'data' => $dashboard
        ]);
    }

    public function revenueAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);
        $endDate = now();

        $analysis = [
            'total_revenue' => $this->getTotalRevenue($startDate, $endDate),
            'revenue_by_service' => $this->getRevenueByService($startDate, $endDate),
            'revenue_by_client_type' => $this->getRevenueByClientType($startDate, $endDate),
            'revenue_by_studio_room' => $this->getRevenueByStudioRoom($startDate, $endDate),
            'monthly_recurring_revenue' => $this->getMonthlyRecurringRevenue(),
            'average_project_value' => $this->getAverageProjectValue($startDate, $endDate),
            'revenue_growth_rate' => $this->getRevenueGrowthRate($startDate, $endDate),
            'seasonal_trends' => $this->getSeasonalTrends(),
            'top_revenue_sources' => $this->getTopRevenueSources($startDate, $endDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $analysis
        ]);
    }

    public function expenseTracking(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $expenses = [
            'total_expenses' => $this->getTotalExpenses($startDate),
            'expense_categories' => $this->getExpenseCategories($startDate),
            'staff_costs' => $this->getStaffCosts($startDate),
            'equipment_costs' => $this->getEquipmentCosts($startDate),
            'operational_costs' => $this->getOperationalCosts($startDate),
            'expense_trends' => $this->getExpenseTrends($startDate),
            'cost_per_project' => $this->getCostPerProject($startDate),
            'budget_variance' => $this->getBudgetVariance($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $expenses
        ]);
    }

    public function profitabilityAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $profitability = [
            'gross_profit' => $this->getGrossProfit($startDate),
            'net_profit' => $this->getNetProfit($startDate),
            'profit_margin' => $this->getProfitMargin($startDate),
            'project_profitability' => $this->getProjectProfitabilityDetailed($startDate),
            'client_profitability' => $this->getClientProfitability($startDate),
            'service_profitability' => $this->getServiceProfitability($startDate),
            'break_even_analysis' => $this->getBreakEvenAnalysis(),
            'roi_analysis' => $this->getROIAnalysis($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $profitability
        ]);
    }

    public function cashFlowAnalysis(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $cashFlow = [
            'operating_cash_flow' => $this->getOperatingCashFlow($startDate),
            'accounts_receivable' => $this->getAccountsReceivable(),
            'accounts_payable' => $this->getAccountsPayable(),
            'cash_flow_forecast' => $this->getCashFlowForecast(),
            'payment_terms_analysis' => $this->getPaymentTermsAnalysis(),
            'collection_efficiency' => $this->getCollectionEfficiency(),
            'working_capital' => $this->getWorkingCapital()
        ];

        return response()->json([
            'success' => true,
            'data' => $cashFlow
        ]);
    }

    public function clientFinancialProfile(Request $request, Client $client)
    {
        $profile = [
            'total_revenue' => $client->total_spent,
            'average_project_value' => $client->getAverageProjectValue(),
            'payment_history' => $this->getClientPaymentHistory($client),
            'outstanding_invoices' => $this->getClientOutstandingInvoices($client),
            'profitability_score' => $this->getClientProfitabilityScore($client),
            'lifetime_value' => $this->getClientLifetimeValue($client),
            'payment_behavior' => $this->getClientPaymentBehavior($client),
            'revenue_trend' => $this->getClientRevenueTrend($client)
        ];

        return response()->json([
            'success' => true,
            'data' => $profile
        ]);
    }

    // Private helper methods for calculations
    private function getRevenueMetrics($startDate, $endDate): array
    {
        $totalRevenue = Booking::whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'confirmed'])
            ->sum('total_cost');

        $previousPeriod = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $previousRevenue = Booking::whereBetween('start_time', [$previousPeriod, $startDate])
            ->whereIn('status', ['completed', 'confirmed'])
            ->sum('total_cost');

        $growthRate = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return [
            'total' => $totalRevenue,
            'growth_rate' => $growthRate,
            'average_daily' => $totalRevenue / $startDate->diffInDays($endDate),
            'projected_monthly' => $totalRevenue * (30 / $startDate->diffInDays($endDate))
        ];
    }

    private function getExpenseMetrics($startDate, $endDate): array
    {
        // Calculate staff costs based on hourly rates and worked hours
        $staffCosts = User::where('status', 'active')
            ->whereNotNull('hourly_rate')
            ->sum(DB::raw('hourly_rate * 160')); // Assuming 160 hours per month

        // Equipment and operational costs (would be from expense tracking system)
        $equipmentCosts = 50000; // Monthly equipment depreciation
        $operationalCosts = 75000; // Monthly operational expenses

        $totalExpenses = $staffCosts + $equipmentCosts + $operationalCosts;

        return [
            'total' => $totalExpenses,
            'staff_costs' => $staffCosts,
            'equipment_costs' => $equipmentCosts,
            'operational_costs' => $operationalCosts,
            'cost_per_day' => $totalExpenses / 30
        ];
    }

    private function getProfitMetrics($startDate, $endDate): array
    {
        $revenue = $this->getRevenueMetrics($startDate, $endDate);
        $expenses = $this->getExpenseMetrics($startDate, $endDate);

        $grossProfit = $revenue['total'] - $expenses['total'];
        $profitMargin = $revenue['total'] > 0 ? ($grossProfit / $revenue['total']) * 100 : 0;

        return [
            'gross_profit' => $grossProfit,
            'profit_margin' => $profitMargin,
            'break_even_point' => $expenses['total'],
            'target_profit' => $expenses['total'] * 0.3 // 30% profit target
        ];
    }

    private function getCashFlowMetrics($startDate, $endDate): array
    {
        $revenue = $this->getRevenueMetrics($startDate, $endDate);
        $expenses = $this->getExpenseMetrics($startDate, $endDate);

        return [
            'net_cash_flow' => $revenue['total'] - $expenses['total'],
            'operating_cash_flow' => $revenue['total'] * 0.85, // Assuming 85% collection rate
            'cash_conversion_cycle' => 15, // Days to convert sales to cash
            'burn_rate' => $expenses['cost_per_day']
        ];
    }

    private function getOperatingCashFlow($startDate): array
    {
        // Calculate operating cash flow components
        $revenue = $this->getRevenueMetrics($startDate, now());
        $expenses = $this->getExpenseMetrics($startDate, now());

        // Cash received from customers (assuming 85% collection rate)
        $cashFromCustomers = $revenue['total'] * 0.85;

        // Cash paid to suppliers and employees
        $cashToSuppliers = $expenses['equipment_costs'] + $expenses['operational_costs'];
        $cashToEmployees = $expenses['staff_costs'];

        // Net operating cash flow
        $netOperatingCash = $cashFromCustomers - $cashToSuppliers - $cashToEmployees;

        return [
            'cash_from_customers' => $cashFromCustomers,
            'cash_to_suppliers' => $cashToSuppliers,
            'cash_to_employees' => $cashToEmployees,
            'net_operating_cash' => $netOperatingCash,
            'operating_margin' => $revenue['total'] > 0 ? ($netOperatingCash / $revenue['total']) * 100 : 0
        ];
    }

    private function getClientFinancialAnalytics($startDate, $endDate): array
    {
        $topClients = Client::select([
                'clients.id',
                'clients.name',
                'clients.email',
                'clients.company',
                'clients.type',
                DB::raw('SUM(bookings.total_cost) as total_revenue'),
                DB::raw('COUNT(DISTINCT projects.id) as project_count'),
                DB::raw('COUNT(DISTINCT bookings.id) as booking_count')
            ])
            ->join('projects', 'clients.id', '=', 'projects.client_id')
            ->join('bookings', 'projects.id', '=', 'bookings.project_id')
            ->whereBetween('bookings.start_time', [$startDate, $endDate])
            ->groupBy('clients.id', 'clients.name', 'clients.email', 'clients.company', 'clients.type')
            ->orderByDesc('total_revenue')
            ->limit(10)
            ->get();

        return [
            'top_clients' => $topClients,
            'client_concentration_risk' => $this->getClientConcentrationRisk(),
            'average_client_value' => Client::avg('total_spent'),
            'client_retention_rate' => $this->getClientRetentionRate()
        ];
    }

    private function getProjectProfitability($startDate, $endDate): array
    {
        return Project::with(['client', 'bookings'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get()
            ->map(function ($project) {
                $revenue = $project->bookings->sum('total_cost');
                $costs = $project->total_cost;
                $profit = $revenue - $costs;
                $margin = $revenue > 0 ? ($profit / $revenue) * 100 : 0;

                return [
                    'project_id' => $project->id,
                    'project_title' => $project->title,
                    'client_name' => $project->client->name,
                    'revenue' => $revenue,
                    'costs' => $costs,
                    'profit' => $profit,
                    'margin' => $margin
                ];
            })
            ->sortByDesc('profit')
            ->values()
            ->toArray();
    }

    private function getStudioUtilizationRevenue($startDate, $endDate): array
    {
        return DB::table('studio_rooms')
            ->leftJoin('bookings', function ($join) use ($startDate, $endDate) {
                $join->on('studio_rooms.id', '=', 'bookings.studio_room_id')
                     ->whereBetween('bookings.start_time', [$startDate, $endDate])
                     ->whereIn('bookings.status', ['completed', 'confirmed']);
            })
            ->select(
                'studio_rooms.name',
                'studio_rooms.hourly_rate',
                DB::raw('COUNT(bookings.id) as total_bookings'),
                DB::raw('SUM(bookings.total_cost) as total_revenue'),
                DB::raw('AVG(TIMESTAMPDIFF(HOUR, bookings.start_time, bookings.end_time)) as avg_session_duration')
            )
            ->groupBy('studio_rooms.id', 'studio_rooms.name', 'studio_rooms.hourly_rate')
            ->get()
            ->toArray();
    }

    private function getRevenueForecast(): array
    {
        // Simple forecasting based on historical data
        $lastMonthRevenue = Booking::whereBetween('start_time', [now()->subMonth(), now()])
            ->whereIn('status', ['completed', 'confirmed'])
            ->sum('total_cost');

        $growthRate = 0.05; // 5% monthly growth assumption

        return [
            'next_month' => $lastMonthRevenue * (1 + $growthRate),
            'next_quarter' => $lastMonthRevenue * 3 * (1 + $growthRate),
            'next_year' => $lastMonthRevenue * 12 * (1 + $growthRate),
            'confidence_level' => 75 // Percentage confidence in forecast
        ];
    }

    private function getFinancialTrends($startDate, $endDate): array
    {
        $days = $startDate->diffInDays($endDate);
        $trends = [];

        for ($i = 0; $i < $days; $i++) {
            $date = $startDate->copy()->addDays($i);
            $dayRevenue = Booking::whereDate('start_time', $date)
                ->whereIn('status', ['completed', 'confirmed'])
                ->sum('total_cost');

            $trends[] = [
                'date' => $date->format('Y-m-d'),
                'revenue' => $dayRevenue
            ];
        }

        return $trends;
    }

    // Additional helper methods would be implemented here...
    private function getClientConcentrationRisk(): float
    {
        $totalRevenue = Client::sum('total_spent');
        $topClientRevenue = Client::orderByDesc('total_spent')->first()->total_spent ?? 0;
        
        return $totalRevenue > 0 ? ($topClientRevenue / $totalRevenue) * 100 : 0;
    }

    private function getClientRetentionRate(): float
    {
        $totalClients = Client::count();
        $activeClients = Client::where('last_project_at', '>=', now()->subMonths(6))->count();
        
        return $totalClients > 0 ? ($activeClients / $totalClients) * 100 : 0;
    }
}
