@extends('layouts.dashboard')

@section('title', 'Chat')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Team Chat</h1>
    <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2" onclick="openCreateChannelModal()">
        <i class="fas fa-plus"></i>
        <span>Create Channel</span>
    </button>
</div>

            <div class="row">
                <!-- Channels Sidebar -->
                <div class="col-md-3">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="mb-0">Channels</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @forelse($channels as $channel)
                                <a href="#" class="list-group-item list-group-item-action channel-item" 
                                   data-channel-id="{{ $channel->id }}" onclick="loadChannel({{ $channel->id }})">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"># {{ $channel->name }}</h6>
                                            <small class="text-muted">{{ $channel->description ?? 'No description' }}</small>
                                        </div>
                                        @if($channel->unread_count > 0)
                                        <span class="badge bg-primary">{{ $channel->unread_count }}</span>
                                        @endif
                                    </div>
                                </a>
                                @empty
                                <div class="p-3 text-center text-muted">
                                    <i class="fas fa-comments fa-2x mb-2"></i>
                                    <p>No channels yet</p>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>

                    <!-- Online Users -->
                    <div class="card shadow mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Online Users</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                @foreach($onlineUsers as $user)
                                <div class="list-group-item">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <div class="avatar-title rounded-circle bg-success text-white">
                                                {{ substr($user->name, 0, 1) }}
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ $user->name }}</h6>
                                            <small class="text-success">Online</small>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Area -->
                <div class="col-md-9">
                    <div class="card shadow" style="height: 600px;">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div id="channel-header">
                                <h6 class="mb-0">Select a channel to start chatting</h6>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="refreshMessages()">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="toggleChannelInfo()">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Messages Area -->
                        <div class="card-body d-flex flex-column p-0" style="height: 500px;">
                            <div id="messages-container" class="flex-grow-1 p-3" style="overflow-y: auto; height: 400px;">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-comments fa-3x mb-3"></i>
                                    <p>Select a channel to view messages</p>
                                </div>
                            </div>
                            
                            <!-- Message Input -->
                            <div class="border-top p-3">
                                <form id="message-form" onsubmit="sendMessage(event)">
                                    <div class="input-group">
                                        <input type="text" id="message-input" class="form-control" 
                                               placeholder="Type your message..." disabled>
                                        <button type="submit" class="btn btn-primary" disabled>
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Channel Modal -->
<div class="modal fade" id="createChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Channel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('chat.create-channel') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="channel_name" class="form-label">Channel Name</label>
                        <input type="text" class="form-control" id="channel_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="channel_description" class="form-label">Description</label>
                        <textarea class="form-control" id="channel_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="channel_type" class="form-label">Channel Type</label>
                        <select class="form-select" id="channel_type" name="type">
                            <option value="public">Public</option>
                            <option value="private">Private</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Channel</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let currentChannelId = null;
let messagesContainer = document.getElementById('messages-container');
let messageInput = document.getElementById('message-input');
let messageForm = document.getElementById('message-form');

function loadChannel(channelId) {
    currentChannelId = channelId;
    
    // Update active channel
    document.querySelectorAll('.channel-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-channel-id="${channelId}"]`).classList.add('active');
    
    // Enable message input
    messageInput.disabled = false;
    messageForm.querySelector('button').disabled = false;
    
    // Load messages
    fetch(`/chat/channels/${channelId}/messages`)
        .then(response => response.json())
        .then(data => {
            displayMessages(data.messages);
            updateChannelHeader(data.channel);
        })
        .catch(error => {
            console.error('Error loading messages:', error);
        });
}

function displayMessages(messages) {
    messagesContainer.innerHTML = '';
    
    messages.forEach(message => {
        const messageElement = createMessageElement(message);
        messagesContainer.appendChild(messageElement);
    });
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function createMessageElement(message) {
    const div = document.createElement('div');
    div.className = 'message mb-3';

    const currentUserId = {{ auth()->id() }};
    const messageUserId = message.user_id || (message.user && message.user.id);
    const isCurrentUser = messageUserId === currentUserId;

    // Handle user data whether it's an object or array
    let userName = '';
    if (message.user) {
        if (typeof message.user === 'object' && message.user.name) {
            userName = message.user.name;
        } else if (typeof message.user === 'object' && message.user.first_name) {
            userName = message.user.first_name + (message.user.last_name ? ' ' + message.user.last_name : '');
        } else if (Array.isArray(message.user) && message.user.length > 0) {
            userName = message.user[0].name || message.user[0].first_name || 'Unknown User';
        }
    }

    div.innerHTML = `
        <div class="d-flex ${isCurrentUser ? 'justify-content-end' : ''}">
            <div class="message-content ${isCurrentUser ? 'bg-primary text-white' : 'bg-light'} rounded p-2" style="max-width: 70%;">
                ${!isCurrentUser ? `<small class="fw-bold">${userName}</small><br>` : ''}
                <div>${message.content || message.message || ''}</div>
                <small class="text-muted">${formatTime(message.created_at)}</small>
            </div>
        </div>
    `;

    return div;
}

// Real-time chat functionality
let websocket = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let typingTimeout = null;
let isTyping = false;

function initializeWebSocket() {
    // For production, use WebSocket server (Pusher, Socket.io, etc.)
    // For now, we'll use polling for real-time updates
    startPolling();
}

function startPolling() {
    setInterval(() => {
        if (currentChannelId) {
            checkForNewMessages();
        }
    }, 3000); // Poll every 3 seconds
}

function checkForNewMessages() {
    const lastMessageId = getLastMessageId();

    fetch(`/api/chat/channels/${currentChannelId}/messages?after=${lastMessageId}`, {
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.messages.length > 0) {
            data.data.messages.forEach(message => {
                addMessageToChat(message);
            });
            scrollToBottom();
        }
    })
    .catch(error => {
        console.error('Error checking for new messages:', error);
    });
}

function getLastMessageId() {
    const messages = document.querySelectorAll('.message-item');
    if (messages.length > 0) {
        return messages[messages.length - 1].dataset.messageId || 0;
    }
    return 0;
}

function sendMessage(event) {
    event.preventDefault();

    if (!currentChannelId || !messageInput.value.trim()) {
        return;
    }

    const content = messageInput.value.trim();
    messageInput.value = '';

    // Show typing indicator
    stopTyping();

    // Add optimistic message
    const tempMessage = {
        id: 'temp-' + Date.now(),
        message: content,
        user: {
            id: currentUserId,
            first_name: currentUserName,
            avatar: currentUserAvatar
        },
        created_at: new Date().toISOString(),
        type: 'text',
        sending: true
    };

    addMessageToChat(tempMessage);
    scrollToBottom();

    fetch(`/api/chat/channels/${currentChannelId}/messages`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ message: content })
    })
    .then(response => response.json())
    .then(data => {
        // Remove temporary message
        const tempElement = document.querySelector(`[data-message-id="temp-${tempMessage.id.split('-')[1]}"]`);
        if (tempElement) {
            tempElement.remove();
        }

        if (data.success) {
            addMessageToChat(data.data.message);
            scrollToBottom();
        } else {
            showNotification('Failed to send message: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
        showNotification('Failed to send message', 'error');

        // Remove temporary message on error
        const tempElement = document.querySelector(`[data-message-id="temp-${tempMessage.id.split('-')[1]}"]`);
        if (tempElement) {
            tempElement.remove();
        }
    });
}

function addMessageToChat(message) {
    const messageElement = createMessageElement(message);
    messagesContainer.appendChild(messageElement);
}

function handleTyping() {
    if (!isTyping) {
        isTyping = true;
        sendTypingIndicator(true);
    }

    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        stopTyping();
    }, 2000);
}

function stopTyping() {
    if (isTyping) {
        isTyping = false;
        sendTypingIndicator(false);
    }
    clearTimeout(typingTimeout);
}

function sendTypingIndicator(typing) {
    if (!currentChannelId) return;

    fetch(`/api/chat/channels/${currentChannelId}/typing`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ typing: typing })
    })
    .catch(error => {
        console.error('Error sending typing indicator:', error);
    });
}
    .catch(error => {
        console.error('Error sending message:', error);
    });
}

function updateChannelHeader(channel) {
    document.getElementById('channel-header').innerHTML = `
        <h6 class="mb-0"># ${channel.name}</h6>
        <small class="text-muted">${channel.description || 'No description'}</small>
    `;
}

function refreshMessages() {
    if (currentChannelId) {
        loadChannel(currentChannelId);
    }
}

function toggleChannelInfo() {
    // Implement channel info toggle
    console.log('Toggle channel info');
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Auto-refresh messages every 5 seconds
setInterval(() => {
    if (currentChannelId) {
        refreshMessages();
    }
}, 5000);

// Handle Enter key in message input
messageInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage(e);
    }
});
</script>
@endpush

@push('styles')
<style>
.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.channel-item.active {
    background-color: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

.message-content {
    word-wrap: break-word;
}

#messages-container::-webkit-scrollbar {
    width: 6px;
}

#messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#messages-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

#messages-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>
@endpush
